#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债量化策略 - 深度优化版本
目标：提高胜率、年化收益率、降低回撤
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关库
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedConvertibleBondStrategy:
    """优化的可转债量化策略类"""
    
    def __init__(self):
        self.bond_data = None
        self.benchmark_data = None
        self.trading_cost = 0.003  # 双边千3
        self.position_size = 30  # 持仓30只左右
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        
        try:
            # 读取转债数据
            print("正在读取转债数据（文件较大，请稍候）...")
            sample_data = pd.read_excel('/Users/<USER>/Desktop/转债数据.xlsx', nrows=5)
            print(f"数据列名：{list(sample_data.columns)}")
            
            self.bond_data = pd.read_excel('/Users/<USER>/Desktop/转债数据.xlsx', engine='openpyxl')
            print(f"转债数据加载成功，形状：{self.bond_data.shape}")
            print(f"时间范围：{self.bond_data['交易日期'].min()} 到 {self.bond_data['交易日期'].max()}")
        except Exception as e:
            print(f"转债数据加载失败：{e}")
            return False
            
        try:
            # 读取基准指数数据
            self.benchmark_data = pd.read_excel('/Users/<USER>/Desktop/中证指数行情.xlsx')
            print(f"基准数据加载成功，形状：{self.benchmark_data.shape}")
            if self.benchmark_data.shape[1] == 3:
                self.benchmark_data = self.benchmark_data.iloc[:, 1:]
            self.benchmark_data.columns = ['日期', '收盘价']
            print(f"时间范围：{self.benchmark_data['日期'].min()} 到 {self.benchmark_data['日期'].max()}")
        except Exception as e:
            print(f"基准数据加载失败：{e}")
            return False
            
        return True
    
    def data_preprocessing(self):
        """数据预处理"""
        print("\n=== 数据预处理 ===")
        
        # 转债数据预处理
        if self.bond_data is not None:
            self.bond_data['交易日期'] = pd.to_datetime(self.bond_data['交易日期'])
            self.bond_data['日成交金额'] = self.bond_data['成交量'] * self.bond_data['收盘价']
            print("转债数据预处理完成")
            
        # 基准数据预处理
        if self.benchmark_data is not None:
            self.benchmark_data['日期'] = pd.to_datetime(self.benchmark_data['日期'])
            self.benchmark_data = self.benchmark_data.sort_values('日期').reset_index(drop=True)
            print("基准数据预处理完成")
    
    def enhanced_factor_engineering(self):
        """增强因子工程 - 大幅扩展因子库"""
        print("\n=== 增强因子工程 ===")
        
        # 确保数据按日期和代码排序
        self.bond_data = self.bond_data.sort_values(['转债代码', '交易日期']).reset_index(drop=True)
        grouped = self.bond_data.groupby('转债代码')
        
        print("计算价格动量因子...")
        # 1. 价格动量因子（多时间窗口）
        for period in [1, 3, 5, 10, 20, 60]:
            self.bond_data[f'收益率_{period}d'] = grouped['收盘价'].pct_change(period)
            self.bond_data[f'反转因子_{period}d'] = -self.bond_data[f'收益率_{period}d']
        
        print("计算估值因子...")
        # 2. 估值因子（多维度）
        for period in [20, 60, 120, 252]:
            self.bond_data[f'双低_分位数_{period}d'] = grouped['双低'].transform(
                lambda x: x.rolling(window=period, min_periods=period//4).rank(pct=True)
            )
            self.bond_data[f'转股溢价率_分位数_{period}d'] = grouped['转股溢价率'].transform(
                lambda x: x.rolling(window=period, min_periods=period//4).rank(pct=True)
            )
            self.bond_data[f'纯债溢价率_分位数_{period}d'] = grouped['纯债溢价率'].transform(
                lambda x: x.rolling(window=period, min_periods=period//4).rank(pct=True)
            )
        
        print("计算技术指标因子...")
        # 3. 技术指标因子
        for period in [5, 10, 20, 60]:
            # 移动平均
            self.bond_data[f'价格_MA{period}'] = grouped['收盘价'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).mean()
            )
            self.bond_data[f'价格偏离度_{period}d'] = (
                self.bond_data['收盘价'] - self.bond_data[f'价格_MA{period}']
            ) / self.bond_data[f'价格_MA{period}']
            
            # 波动率
            self.bond_data[f'波动率_{period}d'] = grouped['收益率_1d'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).std()
            )
            
            # 最高最低价比率
            self.bond_data[f'最高价_MA{period}'] = grouped['最高价'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).max()
            )
            self.bond_data[f'最低价_MA{period}'] = grouped['最低价'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).min()
            )
            self.bond_data[f'价格位置_{period}d'] = (
                self.bond_data['收盘价'] - self.bond_data[f'最低价_MA{period}']
            ) / (self.bond_data[f'最高价_MA{period}'] - self.bond_data[f'最低价_MA{period}'])
        
        print("计算流动性因子...")
        # 4. 流动性因子
        for period in [5, 10, 20]:
            self.bond_data[f'换手率_MA{period}'] = grouped['换手率'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).mean()
            )
            self.bond_data[f'成交金额_MA{period}'] = grouped['日成交金额'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).mean()
            )
            self.bond_data[f'振幅_MA{period}'] = grouped['振幅'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).mean()
            )
        
        # 换手率比值
        self.bond_data['换手率比值_5_20'] = self.bond_data['换手率_MA5'] / self.bond_data['换手率_MA20']
        self.bond_data['换手率比值_10_60'] = self.bond_data['换手率_MA10'] / (
            grouped['换手率'].transform(lambda x: x.rolling(window=60, min_periods=30).mean())
        )
        
        print("计算相对强度因子...")
        # 5. 相对强度因子
        # 行业相对强度
        self.bond_data['双低_行业分位数'] = self.bond_data.groupby(['交易日期', '申万行业'])['双低'].transform(
            lambda x: x.rank(pct=True)
        )
        self.bond_data['收益率_行业分位数'] = self.bond_data.groupby(['交易日期', '申万行业'])['收益率_5d'].transform(
            lambda x: x.rank(pct=True)
        )
        
        # 市场相对强度
        self.bond_data['双低_市场分位数'] = self.bond_data.groupby('交易日期')['双低'].transform(
            lambda x: x.rank(pct=True)
        )
        self.bond_data['收益率_市场分位数'] = self.bond_data.groupby('交易日期')['收益率_5d'].transform(
            lambda x: x.rank(pct=True)
        )
        
        print("计算趋势因子...")
        # 6. 趋势因子
        for period in [10, 20, 60]:
            # 趋势强度
            self.bond_data[f'趋势强度_{period}d'] = grouped['收益率_1d'].transform(
                lambda x: x.rolling(window=period, min_periods=period//2).apply(
                    lambda y: (y > 0).sum() / len(y) if len(y) > 0 else 0.5
                )
            )
            
            # 价格趋势
            self.bond_data[f'价格趋势_{period}d'] = (
                self.bond_data['收盘价'] / grouped['收盘价'].transform(
                    lambda x: x.rolling(window=period, min_periods=period//2).mean()
                ) - 1
            )
        
        print("计算正股相关因子...")
        # 7. 正股相关因子
        self.bond_data['正股收益率_1d'] = grouped['正股收盘价'].pct_change(1)
        self.bond_data['正股收益率_5d'] = grouped['正股收盘价'].pct_change(5)
        self.bond_data['正股收益率_20d'] = grouped['正股收盘价'].pct_change(20)
        
        # 转债与正股相关性
        for period in [20, 60]:
            self.bond_data[f'转债正股相关性_{period}d'] = grouped.apply(
                lambda group: group['收益率_1d'].rolling(window=period, min_periods=period//2).corr(
                    group['正股收益率_1d']
                )
            ).reset_index(level=0, drop=True)
        
        print("因子工程完成！")
        
        # 填充缺失值
        numeric_columns = self.bond_data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            self.bond_data[col] = self.bond_data[col].fillna(self.bond_data[col].median())
        
        print(f"总计算因子数量：{len([col for col in self.bond_data.columns if any(x in col for x in ['收益率', '分位数', '偏离度', '波动率', '位置', '比值', '强度', '趋势', '相关性'])])}")

    def relaxed_stock_screening(self, date):
        """放宽的择券筛选 - 扩大股票池"""
        daily_data = self.bond_data[self.bond_data['交易日期'] == date].copy()

        if daily_data.empty:
            return pd.DataFrame()

        print(f"  原始股票池: {len(daily_data)}只")

        # 放宽的筛选条件 - 优先保证股票池数量
        # 1. 基础质量筛选（相对宽松）
        basic_filter = (
            (daily_data['隐含评级'].isin(['AA-', 'AA', 'AA+', 'AAA', 'A+', 'A'])) &  # 放宽评级
            (daily_data['转债余额'] > 50000000) &  # 降低余额要求到5000万
            (daily_data['剩余期限'] > 0.3) &  # 降低期限要求
            (daily_data['收盘价'] > 50) &  # 避免极低价格
            (daily_data['收盘价'] < 300) &  # 放宽价格上限
            (daily_data['转股溢价率'] < 50) &  # 放宽溢价率
            (daily_data['成交量'] > 0) &  # 有成交量
            (daily_data['成交量'].notna())  # 成交量不为空
        )

        filtered_data = daily_data[basic_filter].copy()
        print(f"  基础筛选后: {len(filtered_data)}只")

        # 2. 如果股票数量充足，进一步筛选
        if len(filtered_data) > 100:
            quality_filter = (
                (filtered_data['转债余额'] > 100000000) &  # 提高余额要求
                (filtered_data['剩余期限'] > 0.5) &  # 提高期限要求
                (filtered_data['转股溢价率'] < 30) &  # 收紧溢价率
                (filtered_data['日成交金额'] > 10000000)  # 日成交金额>1000万
            )
            quality_filtered = filtered_data[quality_filter]

            if len(quality_filtered) >= 50:
                filtered_data = quality_filtered
                print(f"  质量筛选后: {len(filtered_data)}只")

        # 3. 如果股票数量仍然很多，按流动性排序取前200只
        if len(filtered_data) > 200:
            filtered_data = filtered_data.nlargest(200, '日成交金额')
            print(f"  流动性筛选后: {len(filtered_data)}只")

        return filtered_data

    def prepare_enhanced_features(self):
        """准备增强的机器学习特征"""
        print("\n=== 准备增强机器学习特征 ===")

        # 选择最有效的因子
        self.feature_columns = [
            # 价格动量因子
            '收益率_1d', '收益率_3d', '收益率_5d', '收益率_10d', '收益率_20d', '收益率_60d',
            '反转因子_1d', '反转因子_3d', '反转因子_5d', '反转因子_10d', '反转因子_20d',

            # 估值因子
            '双低_分位数_20d', '双低_分位数_60d', '双低_分位数_120d', '双低_分位数_252d',
            '转股溢价率_分位数_20d', '转股溢价率_分位数_60d', '转股溢价率_分位数_120d',
            '纯债溢价率_分位数_20d', '纯债溢价率_分位数_60d',

            # 技术指标因子
            '价格偏离度_5d', '价格偏离度_10d', '价格偏离度_20d', '价格偏离度_60d',
            '波动率_5d', '波动率_10d', '波动率_20d', '波动率_60d',
            '价格位置_5d', '价格位置_10d', '价格位置_20d', '价格位置_60d',

            # 流动性因子
            '换手率_MA5', '换手率_MA10', '换手率_MA20',
            '成交金额_MA5', '成交金额_MA10', '成交金额_MA20',
            '振幅_MA5', '振幅_MA10', '振幅_MA20',
            '换手率比值_5_20', '换手率比值_10_60',

            # 相对强度因子
            '双低_行业分位数', '收益率_行业分位数', '双低_市场分位数', '收益率_市场分位数',

            # 趋势因子
            '趋势强度_10d', '趋势强度_20d', '趋势强度_60d',
            '价格趋势_10d', '价格趋势_20d', '价格趋势_60d',

            # 正股相关因子
            '正股收益率_1d', '正股收益率_5d', '正股收益率_20d',
            '转债正股相关性_20d', '转债正股相关性_60d',

            # 基础因子
            '转股溢价率', '纯债溢价率', '双低', '剩余期限', '隐含波动率',
            '换手率', '振幅', 'PE(LYR)', 'PB(LF)'
        ]

        # 过滤存在的特征
        available_features = [col for col in self.feature_columns if col in self.bond_data.columns]
        self.feature_columns = available_features

        # 计算未来收益率作为标签
        self.bond_data['未来5日收益率'] = self.bond_data.groupby('转债代码')['收盘价'].transform(
            lambda x: x.shift(-5) / x - 1
        )

        # 准备机器学习数据
        required_columns = self.feature_columns + ['未来5日收益率', '交易日期', '转债代码', '转债简称', '收盘价']
        self.ml_data = self.bond_data[required_columns].copy()

        # 填充缺失值
        for col in self.feature_columns:
            if col in self.ml_data.columns:
                self.ml_data[col] = self.ml_data[col].fillna(self.ml_data[col].median())

        self.ml_data = self.ml_data.dropna(subset=['未来5日收益率'])

        print(f"机器学习数据形状：{self.ml_data.shape}")
        print(f"特征数量：{len(self.feature_columns)}")
        print(f"实际可用特征数量：{len(available_features)}")

    def build_advanced_ml_model(self, train_data, test_data):
        """构建高级机器学习模型集成"""

        # 准备训练数据
        X_train = train_data[self.feature_columns].fillna(0)
        y_train = train_data['未来5日收益率'].fillna(0)
        X_test = test_data[self.feature_columns].fillna(0)

        if len(X_train) == 0 or len(X_test) == 0:
            return None, None, {}

        try:
            # 数据标准化
            scaler = RobustScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 高级模型集合
            models = {
                'RandomForest': RandomForestRegressor(
                    n_estimators=200, max_depth=15, min_samples_split=5,
                    min_samples_leaf=2, max_features='sqrt', random_state=42, n_jobs=-1
                ),
                'ExtraTrees': ExtraTreesRegressor(
                    n_estimators=200, max_depth=15, min_samples_split=5,
                    min_samples_leaf=2, max_features='sqrt', random_state=42, n_jobs=-1
                ),
                'GradientBoosting': GradientBoostingRegressor(
                    n_estimators=200, max_depth=8, learning_rate=0.05,
                    subsample=0.8, random_state=42
                ),
                'Ridge': Ridge(alpha=0.1),
                'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42)
            }

            # 模型训练和集成
            predictions = []
            model_weights = []

            for name, model in models.items():
                try:
                    if name in ['Ridge', 'ElasticNet']:
                        model.fit(X_train_scaled, y_train)
                        pred = model.predict(X_test_scaled)
                    else:
                        model.fit(X_train, y_train)
                        pred = model.predict(X_test)

                    # 处理预测结果
                    pred = np.nan_to_num(pred, nan=0.0, posinf=0.1, neginf=-0.1)
                    predictions.append(pred)

                    # 简单的权重分配（可以基于历史表现优化）
                    if name == 'RandomForest':
                        model_weights.append(0.3)
                    elif name == 'ExtraTrees':
                        model_weights.append(0.25)
                    elif name == 'GradientBoosting':
                        model_weights.append(0.25)
                    elif name == 'Ridge':
                        model_weights.append(0.1)
                    else:  # ElasticNet
                        model_weights.append(0.1)

                except Exception as e:
                    print(f"  模型{name}训练失败: {e}")
                    continue

            if predictions:
                # 加权集成预测
                ensemble_pred = np.average(predictions, axis=0, weights=model_weights[:len(predictions)])
                return models['RandomForest'], scaler, {'ensemble': 0.0}, ensemble_pred
            else:
                return None, None, {}, None

        except Exception as e:
            print(f"  模型训练失败: {e}")
            return None, None, {}, None

    def optimized_backtest(self):
        """优化的回测引擎"""
        print("\n=== 开始优化回测 ===")

        # 设置回测参数
        start_date = pd.to_datetime('2021-01-04')
        end_date = pd.to_datetime('2025-07-11')

        # 获取回测期间的交易日
        backtest_data = self.ml_data[
            (self.ml_data['交易日期'] >= start_date) &
            (self.ml_data['交易日期'] <= end_date)
        ].copy()

        # 获取所有交易日并找出周五
        trading_days = sorted(backtest_data['交易日期'].unique())
        rebalance_days = []

        for date in trading_days:
            if date.weekday() == 4:  # 周五
                rebalance_days.append(date)

        print(f"回测期间：{start_date.date()} 到 {end_date.date()}")
        print(f"调仓次数：{len(rebalance_days)}")

        # 初始化回测结果
        portfolio_returns = []
        rebalance_records = []

        # 滚动回测
        for i, rebalance_date in enumerate(rebalance_days[:-1]):
            print(f"处理调仓日 {i+1}/{len(rebalance_days)-1}: {rebalance_date.date()}")

            # 获取训练数据（过去6个月）
            train_end = rebalance_date
            train_start = train_end - pd.Timedelta(days=180)

            train_data = self.ml_data[
                (self.ml_data['交易日期'] >= train_start) &
                (self.ml_data['交易日期'] < train_end)
            ]

            # 获取当日可选股票池
            filtered_stocks = self.relaxed_stock_screening(rebalance_date)

            if len(filtered_stocks) < 30:
                print(f"  可选股票不足30只({len(filtered_stocks)})，跳过")
                continue

            # 训练模型
            if len(train_data) > 1000:
                test_data = filtered_stocks
                model, scaler, _, ensemble_pred = self.build_advanced_ml_model(train_data, test_data)

                if model is not None and ensemble_pred is not None:
                    try:
                        # 使用集成预测结果
                        filtered_stocks['预测收益率'] = ensemble_pred

                        # 风险管理：过滤极端预测值
                        pred_mean = filtered_stocks['预测收益率'].mean()
                        pred_std = filtered_stocks['预测收益率'].std()

                        # 只选择预测收益率在合理范围内的股票
                        reasonable_mask = (
                            (filtered_stocks['预测收益率'] >= pred_mean - 2*pred_std) &
                            (filtered_stocks['预测收益率'] <= pred_mean + 2*pred_std)
                        )
                        filtered_stocks = filtered_stocks[reasonable_mask]

                        if len(filtered_stocks) < 20:
                            print(f"  风险过滤后股票不足，跳过")
                            continue

                        # 选择30只股票
                        n_select = min(30, len(filtered_stocks))
                        selected_stocks = filtered_stocks.nlargest(n_select, '预测收益率')

                        # 记录调仓
                        rebalance_record = {
                            '调仓日期': rebalance_date,
                            '选中股票': selected_stocks[['转债代码', '转债简称', '预测收益率', '收盘价']].to_dict('records')
                        }
                        rebalance_records.append(rebalance_record)

                        # 计算下一周的收益率
                        next_rebalance = rebalance_days[i+1]
                        portfolio_return = self.calculate_portfolio_return_with_risk_mgmt(
                            selected_stocks, rebalance_date, next_rebalance
                        )
                        portfolio_returns.append({
                            '开始日期': rebalance_date,
                            '结束日期': next_rebalance,
                            '组合收益率': portfolio_return
                        })

                        print(f"  成功选择{len(selected_stocks)}只股票，预期收益率范围: {ensemble_pred.min():.3f} ~ {ensemble_pred.max():.3f}")

                    except Exception as e:
                        print(f"  预测失败: {e}")
                        continue
                else:
                    print(f"  模型训练失败")
                    continue
            else:
                print(f"  训练数据不足({len(train_data)})")

        return portfolio_returns, rebalance_records

    def calculate_portfolio_return_with_risk_mgmt(self, selected_stocks, start_date, end_date):
        """带风险管理的组合收益率计算"""
        if len(selected_stocks) == 0:
            return 0

        # 等权重配置
        weight = 1.0 / len(selected_stocks)
        total_return = 0
        valid_stocks = 0

        for _, stock in selected_stocks.iterrows():
            code = stock['转债代码']

            # 获取期间价格数据
            price_data = self.bond_data[
                (self.bond_data['转债代码'] == code) &
                (self.bond_data['交易日期'] >= start_date) &
                (self.bond_data['交易日期'] <= end_date)
            ].sort_values('交易日期')

            if len(price_data) >= 2:
                start_price = price_data.iloc[0]['收盘价']
                end_price = price_data.iloc[-1]['收盘价']

                # 风险管理：限制单只股票的最大损失
                stock_return = end_price / start_price - 1

                # 设置止损线（单只股票最大损失15%）
                if stock_return < -0.15:
                    stock_return = -0.15

                # 设置止盈线（单只股票最大收益50%）
                if stock_return > 0.50:
                    stock_return = 0.50

                total_return += stock_return * weight
                valid_stocks += 1

        # 如果有效股票数量不足，按比例调整
        if valid_stocks > 0:
            total_return = total_return * len(selected_stocks) / valid_stocks

        # 扣除交易成本
        total_return -= self.trading_cost

        return total_return

    def calculate_enhanced_performance_metrics(self, portfolio_returns):
        """计算增强的业绩指标"""
        print("\n=== 计算增强业绩指标 ===")

        # 转换为DataFrame
        returns_df = pd.DataFrame(portfolio_returns)
        returns_df['累计净值'] = (1 + returns_df['组合收益率']).cumprod()

        # 获取基准数据
        benchmark_returns = self.calculate_benchmark_returns(returns_df)

        # 基础指标
        total_return = returns_df['累计净值'].iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / (len(returns_df) * 5)) - 1

        volatility = returns_df['组合收益率'].std() * np.sqrt(52)  # 周频年化
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0

        # 最大回撤
        cumulative = returns_df['累计净值']
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # 胜率
        win_rate = (returns_df['组合收益率'] > 0).mean()

        # 超额收益指标
        excess_returns = returns_df['组合收益率'] - pd.Series(benchmark_returns)
        excess_annual_return = excess_returns.mean() * 52
        tracking_error = excess_returns.std() * np.sqrt(52)
        information_ratio = excess_annual_return / tracking_error if tracking_error > 0 else 0

        # 下行风险指标
        negative_returns = returns_df['组合收益率'][returns_df['组合收益率'] < 0]
        downside_deviation = negative_returns.std() * np.sqrt(52) if len(negative_returns) > 0 else 0
        sortino_ratio = annual_return / downside_deviation if downside_deviation > 0 else 0

        # 卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

        # 最大连续亏损
        consecutive_losses = 0
        max_consecutive_losses = 0
        for ret in returns_df['组合收益率']:
            if ret < 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0

        metrics = {
            '年化收益率': annual_return,
            '年化波动率': volatility,
            '夏普比率': sharpe_ratio,
            '最大回撤': abs(max_drawdown),
            '胜率': win_rate,
            '换手率': 2.0,  # 每周换仓
            '交易周期': len(returns_df),
            '盈利周期': (returns_df['组合收益率'] > 0).sum(),
            '亏损周期': (returns_df['组合收益率'] < 0).sum(),
            '平均每周期收益': returns_df['组合收益率'].mean(),
            '超额年化收益': excess_annual_return,
            '跟踪误差': tracking_error,
            '信息比率': information_ratio,
            '索提诺比率': sortino_ratio,
            '卡玛比率': calmar_ratio,
            '最大连续亏损周期': max_consecutive_losses,
            '下行标准差': downside_deviation
        }

        return metrics, returns_df, benchmark_returns

    def calculate_benchmark_returns(self, returns_df):
        """计算基准收益率"""
        benchmark_returns = []

        for _, row in returns_df.iterrows():
            start_date = row['开始日期']
            end_date = row['结束日期']

            benchmark_data = self.benchmark_data[
                (self.benchmark_data['日期'] >= start_date) &
                (self.benchmark_data['日期'] <= end_date)
            ].sort_values('日期')

            if len(benchmark_data) >= 2:
                start_price = benchmark_data.iloc[0]['收盘价']
                end_price = benchmark_data.iloc[-1]['收盘价']
                benchmark_return = end_price / start_price - 1
            else:
                benchmark_return = 0

            benchmark_returns.append(benchmark_return)

        return benchmark_returns

    def save_optimized_results(self, metrics, returns_df, benchmark_returns, rebalance_records):
        """保存优化结果"""
        print("\n=== 保存优化结果 ===")

        # 创建输出文件夹
        output_dir = "/Users/<USER>/Desktop/转债机器学习回测结果_优化版"
        os.makedirs(output_dir, exist_ok=True)

        # 保存Excel文件
        with pd.ExcelWriter(f"{output_dir}/优化回测结果.xlsx", engine='openpyxl') as writer:
            # Sheet1: 业绩指标
            metrics_df = pd.DataFrame([metrics]).T
            metrics_df.columns = ['数值']
            metrics_df.to_excel(writer, sheet_name='业绩指标')

            # Sheet2: 净值数据
            net_value_df = returns_df.copy()
            net_value_df['基准净值'] = (1 + pd.Series(benchmark_returns)).cumprod()
            net_value_df['策略净值'] = net_value_df['累计净值']
            net_value_df['超额收益'] = net_value_df['组合收益率'] - pd.Series(benchmark_returns)
            net_value_df.to_excel(writer, sheet_name='净值数据', index=False)

            # Sheet3: 调仓记录
            rebalance_df = []
            for record in rebalance_records:
                for stock in record['选中股票']:
                    rebalance_df.append({
                        '调仓日期': record['调仓日期'],
                        '转债代码': stock['转债代码'],
                        '转债简称': stock['转债简称'],
                        '预测收益率': stock['预测收益率'],
                        '收盘价': stock['收盘价']
                    })

            pd.DataFrame(rebalance_df).to_excel(writer, sheet_name='调仓记录', index=False)

        # 生成图表
        self.generate_optimized_charts(returns_df, benchmark_returns, output_dir)

        print(f"优化结果已保存到：{output_dir}")

    def generate_optimized_charts(self, returns_df, benchmark_returns, output_dir):
        """生成优化图表"""

        # 1. 净值走势对比图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

        dates = returns_df['开始日期']
        strategy_nav = returns_df['累计净值']
        benchmark_nav = (1 + pd.Series(benchmark_returns)).cumprod()

        # 上图：净值走势
        ax1.plot(dates, strategy_nav, label='Optimized Strategy', linewidth=2, color='red')
        ax1.plot(dates, benchmark_nav, label='Benchmark', linewidth=2, color='blue')

        # 添加回撤阴影
        running_max = strategy_nav.expanding().max()
        ax1.fill_between(dates, strategy_nav, running_max, alpha=0.3, color='gray')

        ax1.set_title('Optimized Strategy Performance vs Benchmark', fontsize=16, fontweight='bold')
        ax1.set_ylabel('Cumulative Return', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 下图：超额收益
        excess_returns = returns_df['组合收益率'] - pd.Series(benchmark_returns)
        cumulative_excess = (1 + excess_returns).cumprod() - 1

        ax2.plot(dates, cumulative_excess, label='Cumulative Excess Return', linewidth=2, color='green')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.set_title('Cumulative Excess Return', fontsize=14)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Excess Return', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/优化策略净值走势图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 收益率分布图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 周收益率分布
        ax1.hist(returns_df['组合收益率'], bins=30, alpha=0.7, color='red', label='Strategy')
        ax1.hist(benchmark_returns, bins=30, alpha=0.7, color='blue', label='Benchmark')
        ax1.set_title('Weekly Returns Distribution', fontsize=14)
        ax1.set_xlabel('Weekly Return', fontsize=12)
        ax1.set_ylabel('Frequency', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 滚动夏普比率
        rolling_sharpe = returns_df['组合收益率'].rolling(window=12).mean() / returns_df['组合收益率'].rolling(window=12).std() * np.sqrt(52)
        ax2.plot(dates, rolling_sharpe, linewidth=2, color='purple')
        ax2.set_title('Rolling Sharpe Ratio (12-week)', fontsize=14)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Sharpe Ratio', fontsize=12)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/收益率分析图.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("优化图表生成完成")

if __name__ == "__main__":
    # 创建优化策略实例
    strategy = OptimizedConvertibleBondStrategy()

    # 加载数据
    if strategy.load_data():
        # 数据预处理
        strategy.data_preprocessing()

        # 增强因子工程
        strategy.enhanced_factor_engineering()

        # 准备增强特征
        strategy.prepare_enhanced_features()

        # 运行优化回测
        portfolio_returns, rebalance_records = strategy.optimized_backtest()

        if len(portfolio_returns) > 0:
            # 计算业绩指标
            metrics, returns_df, benchmark_returns = strategy.calculate_enhanced_performance_metrics(portfolio_returns)

            # 保存结果
            strategy.save_optimized_results(metrics, returns_df, benchmark_returns, rebalance_records)

            # 打印关键指标
            print(f"\n=== 优化策略业绩总结 ===")
            print(f"年化收益率: {metrics['年化收益率']:.2%}")
            print(f"年化波动率: {metrics['年化波动率']:.2%}")
            print(f"夏普比率: {metrics['夏普比率']:.2f}")
            print(f"最大回撤: {metrics['最大回撤']:.2%}")
            print(f"胜率: {metrics['胜率']:.2%}")
            print(f"信息比率: {metrics['信息比率']:.2f}")
            print(f"索提诺比率: {metrics['索提诺比率']:.2f}")
            print(f"卡玛比率: {metrics['卡玛比率']:.2f}")
        else:
            print("优化回测失败，请检查数据")

        print("\n优化策略回测完成！")
    else:
        print("数据加载失败，请检查文件路径")
