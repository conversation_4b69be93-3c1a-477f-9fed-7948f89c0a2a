#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可转债量化周频轮动策略回测系统
作者：AI Assistant
日期：2025-08-16
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关库
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ConvertibleBondStrategy:
    """可转债量化策略类"""
    
    def __init__(self):
        self.bond_data = None
        self.benchmark_data = None
        self.trading_cost = 0.003  # 双边千3
        self.position_size = (20, 40)  # 持仓容量20-40只
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")

        # 读取转债数据 - 使用分块读取优化大文件
        try:
            print("正在读取转债数据（文件较大，请稍候）...")
            # 先读取少量行来获取列名
            sample_data = pd.read_excel('/Users/<USER>/Desktop/转债数据.xlsx', nrows=5)
            print(f"数据列名：{list(sample_data.columns)}")

            # 读取完整数据
            self.bond_data = pd.read_excel('/Users/<USER>/Desktop/转债数据.xlsx',
                                         engine='openpyxl')
            print(f"转债数据加载成功，形状：{self.bond_data.shape}")
            print(f"时间范围：{self.bond_data['交易日期'].min()} 到 {self.bond_data['交易日期'].max()}")
        except Exception as e:
            print(f"转债数据加载失败：{e}")
            return False

        # 读取基准指数数据
        try:
            self.benchmark_data = pd.read_excel('/Users/<USER>/Desktop/中证指数行情.xlsx')
            print(f"基准数据加载成功，形状：{self.benchmark_data.shape}")
            print(f"列名：{list(self.benchmark_data.columns)}")
            print(f"时间范围：{self.benchmark_data.iloc[:, 0].min()} 到 {self.benchmark_data.iloc[:, 0].max()}")
        except Exception as e:
            print(f"基准数据加载失败：{e}")
            return False

        return True
    
    def data_exploration(self):
        """数据探索分析"""
        print("\n=== 数据探索分析 ===")
        
        if self.bond_data is not None:
            print("\n转债数据基本信息：")
            print(f"数据形状：{self.bond_data.shape}")
            print(f"时间范围：{self.bond_data['交易日期'].min()} 到 {self.bond_data['交易日期'].max()}")
            print(f"转债数量：{self.bond_data['转债代码'].nunique()}")
            print(f"交易日数量：{self.bond_data['交易日期'].nunique()}")
            
            # 检查缺失值
            print("\n缺失值情况：")
            missing_data = self.bond_data.isnull().sum()
            missing_data = missing_data[missing_data > 0].sort_values(ascending=False)
            if len(missing_data) > 0:
                print(missing_data)
            else:
                print("无缺失值")
                
            # 数据类型检查
            print("\n数据类型：")
            print(self.bond_data.dtypes)
            
            # 关键字段统计
            print("\n关键字段统计：")
            key_fields = ['收盘价', '转股溢价率', '纯债溢价率', '双低', '剩余期限', '成交量', '换手率']
            for field in key_fields:
                if field in self.bond_data.columns:
                    print(f"{field}: 均值={self.bond_data[field].mean():.2f}, "
                          f"中位数={self.bond_data[field].median():.2f}, "
                          f"标准差={self.bond_data[field].std():.2f}")
        
        if self.benchmark_data is not None:
            print("\n基准数据基本信息：")
            print(f"数据形状：{self.benchmark_data.shape}")
            print(f"原始列名：{list(self.benchmark_data.columns)}")
            # 重命名列 - 根据实际列数调整
            if self.benchmark_data.shape[1] == 3:
                self.benchmark_data = self.benchmark_data.iloc[:, 1:]  # 去掉第一列
            self.benchmark_data.columns = ['日期', '收盘价']
            print(f"时间范围：{self.benchmark_data['日期'].min()} 到 {self.benchmark_data['日期'].max()}")
            
    def data_preprocessing(self):
        """数据预处理"""
        print("\n=== 数据预处理 ===")
        
        # 转债数据预处理
        if self.bond_data is not None:
            # 确保日期格式正确
            self.bond_data['交易日期'] = pd.to_datetime(self.bond_data['交易日期'])
            
            # 计算日成交金额
            self.bond_data['日成交金额'] = self.bond_data['成交量'] * self.bond_data['收盘价']
            
            # 计算正股价格与强赎线比较
            self.bond_data['正股价格_强赎线比'] = self.bond_data['正股收盘价'] / (self.bond_data['强赎线'] / 100 * self.bond_data['转股价'])
            
            print("转债数据预处理完成")
            
        # 基准数据预处理
        if self.benchmark_data is not None:
            self.benchmark_data['日期'] = pd.to_datetime(self.benchmark_data['日期'])
            self.benchmark_data = self.benchmark_data.sort_values('日期').reset_index(drop=True)
            print("基准数据预处理完成")
    
    def calculate_avg_turnover_5d(self, date):
        """计算近5个交易日的日均成交金额"""
        # 获取该日期前5个交易日的数据
        end_date = date
        start_date = date - pd.Timedelta(days=10)  # 往前推10天确保有5个交易日

        period_data = self.bond_data[
            (self.bond_data['交易日期'] >= start_date) &
            (self.bond_data['交易日期'] <= end_date)
        ].copy()

        if period_data.empty:
            return pd.Series()

        # 计算每日成交金额 = 成交量 * (最高价 + 最低价) / 2
        period_data['日成交金额_修正'] = period_data['成交量'] * (period_data['最高价'] + period_data['最低价']) / 2

        # 计算每只转债近5个交易日的平均成交金额
        # 先按转债代码分组，然后取每组最后5条记录的平均值
        avg_turnover = period_data.groupby('转债代码').apply(
            lambda x: x.tail(5)['日成交金额_修正'].mean()
        )

        return avg_turnover

    def stock_screening(self, date):
        """择券筛选 - 按照用户要求的严格标准"""
        # 获取指定日期的数据
        daily_data = self.bond_data[self.bond_data['交易日期'] == date].copy()

        if daily_data.empty:
            return pd.DataFrame()

        # 计算近5日平均成交金额
        avg_turnover_5d = self.calculate_avg_turnover_5d(date)

        # 将平均成交金额合并到当日数据
        daily_data = daily_data.set_index('转债代码')
        daily_data['近5日平均成交金额'] = avg_turnover_5d
        daily_data = daily_data.reset_index()

        # 填充缺失值
        daily_data['近5日平均成交金额'] = daily_data['近5日平均成交金额'].fillna(0)

        print(f"  原始股票池: {len(daily_data)}只")

        # 按照用户要求的择券条件进行筛选
        conditions = [
            daily_data['隐含评级'].isin(['AA-', 'AA', 'AA+', 'AAA']),  # 隐含评级AA-及以上
            daily_data['转债余额'] > 100000000,  # 转债余额>1亿
            daily_data['近5日平均成交金额'] > 50000,  # 近5日日均成交金额>5万
            daily_data['剩余期限'] > 0.5,  # 剩余期限>0.5Y
            daily_data['收盘价'] < 200,  # 收盘价<200
            daily_data['转股溢价率'] < 30  # 转股溢价率<30%
        ]

        condition_names = [
            '隐含评级AA-及以上',
            '转债余额>1亿',
            '近5日日均成交金额>5万',
            '剩余期限>0.5Y',
            '收盘价<200',
            '转股溢价率<30%'
        ]

        # 逐步应用筛选条件
        filtered_data = daily_data.copy()

        for i, (condition, name) in enumerate(zip(conditions, condition_names)):
            before_count = len(filtered_data)
            filtered_data = filtered_data[condition]
            after_count = len(filtered_data)
            print(f"  {name}: {before_count} -> {after_count}只")

            if after_count == 0:
                print(f"  警告: 应用条件'{name}'后无可选股票")
                break

        print(f"  最终筛选结果: {len(filtered_data)}只转债")

        return filtered_data

    def calculate_factors(self):
        """计算因子"""
        print("\n=== 因子工程 ===")

        # 确保数据按日期和代码排序
        self.bond_data = self.bond_data.sort_values(['转债代码', '交易日期']).reset_index(drop=True)

        # 1. 低估值因子
        print("计算低估值因子...")

        # 1.1 隐含波动率分位数（绝对估值）
        self.bond_data['隐含波动率_历史分位数'] = self.bond_data.groupby('转债代码')['隐含波动率'].transform(
            lambda x: x.rolling(window=252, min_periods=60).rank(pct=True)
        )

        # 1.2 双低历史分位数（绝对估值）
        self.bond_data['双低_历史分位数'] = self.bond_data.groupby('转债代码')['双低'].transform(
            lambda x: x.rolling(window=252, min_periods=60).rank(pct=True)
        )

        # 1.3 转股溢价率历史分位数（绝对估值）
        self.bond_data['转股溢价率_历史分位数'] = self.bond_data.groupby('转债代码')['转股溢价率'].transform(
            lambda x: x.rolling(window=252, min_periods=60).rank(pct=True)
        )

        # 1.4 纯债溢价率历史分位数（绝对估值）
        self.bond_data['纯债溢价率_历史分位数'] = self.bond_data.groupby('转债代码')['纯债溢价率'].transform(
            lambda x: x.rolling(window=252, min_periods=60).rank(pct=True)
        )

        # 1.5 相对估值因子 - 行业内估值分位数
        self.bond_data['双低_行业分位数'] = self.bond_data.groupby(['交易日期', '申万行业'])['双低'].transform(
            lambda x: x.rank(pct=True)
        )

        # 1.6 价格相对历史均值偏离度
        self.bond_data['价格_MA20'] = self.bond_data.groupby('转债代码')['收盘价'].transform(
            lambda x: x.rolling(window=20, min_periods=10).mean()
        )
        self.bond_data['价格偏离度'] = (self.bond_data['收盘价'] - self.bond_data['价格_MA20']) / self.bond_data['价格_MA20']

        # 2. 动量因子
        print("计算动量因子...")

        # 2.1 价格动量（趋势跟踪）
        self.bond_data['收益率_1d'] = self.bond_data.groupby('转债代码')['收盘价'].pct_change(1)
        self.bond_data['收益率_5d'] = self.bond_data.groupby('转债代码')['收盘价'].pct_change(5)
        self.bond_data['收益率_20d'] = self.bond_data.groupby('转债代码')['收盘价'].pct_change(20)
        self.bond_data['收益率_60d'] = self.bond_data.groupby('转债代码')['收盘价'].pct_change(60)

        # 2.2 反转因子
        self.bond_data['反转因子_5d'] = -self.bond_data['收益率_5d']  # 短期反转
        self.bond_data['反转因子_20d'] = -self.bond_data['收益率_20d']  # 中期反转

        # 2.3 动量强度因子
        self.bond_data['动量强度'] = self.bond_data.groupby('转债代码')['收益率_1d'].transform(
            lambda x: x.rolling(window=20, min_periods=10).std()
        )

        # 2.4 趋势一致性因子
        self.bond_data['趋势一致性'] = self.bond_data.groupby('转债代码').apply(
            lambda group: self._calculate_trend_consistency(group)
        ).reset_index(level=0, drop=True)

        # 3. 技术指标因子
        print("计算技术指标因子...")

        # 3.1 RSI相对强弱指数
        self.bond_data['RSI'] = self.bond_data.groupby('转债代码').apply(
            lambda group: self._calculate_rsi(group['收盘价'])
        ).reset_index(level=0, drop=True)

        # 3.2 布林带位置
        self.bond_data['布林带上轨'] = self.bond_data.groupby('转债代码')['收盘价'].transform(
            lambda x: x.rolling(window=20, min_periods=10).mean() + 2 * x.rolling(window=20, min_periods=10).std()
        )
        self.bond_data['布林带下轨'] = self.bond_data.groupby('转债代码')['收盘价'].transform(
            lambda x: x.rolling(window=20, min_periods=10).mean() - 2 * x.rolling(window=20, min_periods=10).std()
        )
        self.bond_data['布林带位置'] = (self.bond_data['收盘价'] - self.bond_data['布林带下轨']) / (
            self.bond_data['布林带上轨'] - self.bond_data['布林带下轨']
        )

        # 4. 流动性因子
        print("计算流动性因子...")

        # 4.1 换手率因子
        self.bond_data['换手率_MA5'] = self.bond_data.groupby('转债代码')['换手率'].transform(
            lambda x: x.rolling(window=5, min_periods=3).mean()
        )
        self.bond_data['换手率_MA20'] = self.bond_data.groupby('转债代码')['换手率'].transform(
            lambda x: x.rolling(window=20, min_periods=10).mean()
        )
        self.bond_data['换手率比值'] = self.bond_data['换手率_MA5'] / self.bond_data['换手率_MA20']

        # 4.2 成交金额因子
        self.bond_data['成交金额_MA5'] = self.bond_data.groupby('转债代码')['日成交金额'].transform(
            lambda x: x.rolling(window=5, min_periods=3).mean()
        )

        print("因子计算完成！")

    def _calculate_trend_consistency(self, group):
        """计算趋势一致性"""
        returns = group['收益率_1d'].fillna(0)
        if len(returns) < 20:
            return pd.Series([np.nan] * len(returns), index=group.index)

        consistency = returns.rolling(window=20, min_periods=10).apply(
            lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0.5
        )
        return consistency

    def _calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=period//2).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=period//2).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def prepare_ml_features(self):
        """准备机器学习特征"""
        print("\n=== 准备机器学习特征 ===")

        # 定义特征列
        self.feature_columns = [
            # 低估值因子
            '隐含波动率_历史分位数', '双低_历史分位数', '转股溢价率_历史分位数',
            '纯债溢价率_历史分位数', '双低_行业分位数', '价格偏离度',

            # 动量因子
            '收益率_5d', '收益率_20d', '收益率_60d', '反转因子_5d', '反转因子_20d',
            '动量强度', '趋势一致性',

            # 技术指标因子
            'RSI', '布林带位置',

            # 流动性因子
            '换手率比值', '换手率_MA5', '成交金额_MA5',

            # 基础因子
            '转股溢价率', '纯债溢价率', '双低', '剩余期限', '隐含波动率',
            '换手率', '振幅', 'PE(LYR)', 'PB(LF)'
        ]

        # 计算未来收益率作为标签
        self.bond_data['未来5日收益率'] = self.bond_data.groupby('转债代码')['收盘价'].transform(
            lambda x: x.shift(-5) / x - 1
        )

        # 去除缺失值
        self.ml_data = self.bond_data.dropna(subset=self.feature_columns + ['未来5日收益率']).copy()

        print(f"机器学习数据形状：{self.ml_data.shape}")
        print(f"特征数量：{len(self.feature_columns)}")

    def build_ml_model(self, train_data, test_data):
        """构建机器学习模型"""

        # 准备训练数据，处理缺失值
        X_train = train_data[self.feature_columns].fillna(0)
        y_train = train_data['未来5日收益率'].fillna(0)

        X_test = test_data[self.feature_columns].fillna(0)
        y_test = test_data['未来5日收益率'].fillna(0)

        # 检查数据有效性
        if len(X_train) == 0 or len(X_test) == 0:
            return None, None, {}

        # 数据标准化
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # 模型集合
        models = {
            'RandomForest': RandomForestRegressor(
                n_estimators=50, max_depth=8, min_samples_split=10,
                random_state=42, n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=50, max_depth=4, learning_rate=0.1,
                random_state=42
            ),
            'Ridge': Ridge(alpha=1.0)
        }

        # 训练模型并选择最佳模型
        best_model = None
        best_score = float('inf')
        model_scores = {}
        best_scaler = None

        for name, model in models.items():
            try:
                if name == 'Ridge':
                    model.fit(X_train_scaled, y_train)
                    pred = model.predict(X_test_scaled)
                    current_scaler = scaler
                else:
                    model.fit(X_train, y_train)
                    pred = model.predict(X_test)
                    current_scaler = None

                # 处理预测结果中的异常值
                pred = np.nan_to_num(pred, nan=0.0, posinf=0.1, neginf=-0.1)
                y_test_clean = np.nan_to_num(y_test, nan=0.0, posinf=0.1, neginf=-0.1)

                mse = mean_squared_error(y_test_clean, pred)
                model_scores[name] = mse

                if mse < best_score:
                    best_score = mse
                    best_model = model
                    best_scaler = current_scaler

            except Exception as e:
                print(f"  模型{name}训练失败: {e}")
                model_scores[name] = float('inf')

        return best_model, best_scaler, model_scores

    def weekly_rebalance_backtest(self):
        """周频调仓回测"""
        print("\n=== 开始回测 ===")

        # 准备机器学习特征
        self.prepare_ml_features()

        # 设置回测参数
        start_date = pd.to_datetime('2021-01-04')
        end_date = pd.to_datetime('2025-07-11')

        # 获取回测期间的交易日
        backtest_data = self.ml_data[
            (self.ml_data['交易日期'] >= start_date) &
            (self.ml_data['交易日期'] <= end_date)
        ].copy()

        # 获取所有交易日并找出周五
        trading_days = sorted(backtest_data['交易日期'].unique())
        rebalance_days = []

        for date in trading_days:
            if date.weekday() == 4:  # 周五
                rebalance_days.append(date)

        print(f"回测期间：{start_date.date()} 到 {end_date.date()}")
        print(f"调仓次数：{len(rebalance_days)}")

        # 初始化回测结果
        portfolio_returns = []
        portfolio_positions = []
        rebalance_records = []

        # 滚动回测
        for i, rebalance_date in enumerate(rebalance_days[:-1]):
            print(f"处理调仓日 {i+1}/{len(rebalance_days)-1}: {rebalance_date.date()}")

            # 获取训练数据（过去252个交易日）
            train_end = rebalance_date
            train_start = train_end - pd.Timedelta(days=365)

            train_data = self.ml_data[
                (self.ml_data['交易日期'] >= train_start) &
                (self.ml_data['交易日期'] < train_end)
            ]

            # 获取当日可选股票池
            filtered_stocks = self.stock_screening(rebalance_date)

            if len(filtered_stocks) < 20:
                print(f"  可选股票不足20只({len(filtered_stocks)})，跳过")
                continue

            # 训练模型
            if len(train_data) > 500:  # 确保有足够的训练数据
                test_data = filtered_stocks
                model, scaler, _ = self.build_ml_model(train_data, test_data)

                if model is not None:
                    # 预测收益率
                    try:
                        if scaler is not None:  # Ridge模型
                            X_pred = scaler.transform(filtered_stocks[self.feature_columns].fillna(0))
                            predictions = model.predict(X_pred)
                        else:
                            predictions = model.predict(filtered_stocks[self.feature_columns].fillna(0))

                        # 处理预测结果
                        predictions = np.nan_to_num(predictions, nan=0.0, posinf=0.1, neginf=-0.1)
                        filtered_stocks['预测收益率'] = predictions

                        # 选择20-40只股票，根据可选股票数量动态调整
                        if len(filtered_stocks) >= 40:
                            n_select = 40
                        elif len(filtered_stocks) >= 30:
                            n_select = 30
                        else:
                            n_select = max(20, len(filtered_stocks))

                        selected_stocks = filtered_stocks.nlargest(n_select, '预测收益率')

                        # 记录调仓
                        rebalance_record = {
                            '调仓日期': rebalance_date,
                            '选中股票': selected_stocks[['转债代码', '转债简称', '预测收益率', '收盘价']].to_dict('records')
                        }
                        rebalance_records.append(rebalance_record)

                        # 计算下一周的收益率
                        next_rebalance = rebalance_days[i+1]
                        portfolio_return = self.calculate_portfolio_return(
                            selected_stocks, rebalance_date, next_rebalance
                        )
                        portfolio_returns.append({
                            '开始日期': rebalance_date,
                            '结束日期': next_rebalance,
                            '组合收益率': portfolio_return
                        })

                        print(f"  成功选择{len(selected_stocks)}只股票，预期收益率范围: {predictions.min():.3f} ~ {predictions.max():.3f}")

                    except Exception as e:
                        print(f"  预测失败: {e}")
                        continue
                else:
                    print(f"  模型训练失败")
                    continue
            else:
                print(f"  训练数据不足({len(train_data)})")

        return portfolio_returns, rebalance_records

    def calculate_portfolio_return(self, selected_stocks, start_date, end_date):
        """计算组合收益率"""
        if len(selected_stocks) == 0:
            return 0

        # 等权重配置
        weight = 1.0 / len(selected_stocks)
        total_return = 0

        for _, stock in selected_stocks.iterrows():
            code = stock['转债代码']

            # 获取期间价格数据
            price_data = self.bond_data[
                (self.bond_data['转债代码'] == code) &
                (self.bond_data['交易日期'] >= start_date) &
                (self.bond_data['交易日期'] <= end_date)
            ].sort_values('交易日期')

            if len(price_data) >= 2:
                start_price = price_data.iloc[0]['收盘价']
                end_price = price_data.iloc[-1]['收盘价']
                stock_return = (end_price / start_price - 1) * weight
                total_return += stock_return

        # 扣除交易成本
        total_return -= self.trading_cost

        return total_return

    def calculate_performance_metrics(self, portfolio_returns):
        """计算业绩指标"""
        print("\n=== 计算业绩指标 ===")

        # 转换为DataFrame
        returns_df = pd.DataFrame(portfolio_returns)
        returns_df['累计净值'] = (1 + returns_df['组合收益率']).cumprod()

        # 获取基准数据
        benchmark_returns = self.calculate_benchmark_returns(returns_df)

        # 计算各项指标
        total_return = returns_df['累计净值'].iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(returns_df)) - 1

        volatility = returns_df['组合收益率'].std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0

        # 最大回撤
        cumulative = returns_df['累计净值']
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # 胜率
        win_rate = (returns_df['组合收益率'] > 0).mean()

        # 换手率（简化计算）
        turnover_rate = 2.0  # 假设每周完全换仓

        metrics = {
            '年化收益率': annual_return,
            '年化波动': volatility,
            '夏普比率': sharpe_ratio,
            '最大回撤': abs(max_drawdown),
            '胜率': win_rate,
            '换手率': turnover_rate,
            '交易周期': len(returns_df),
            '盈利周期': (returns_df['组合收益率'] > 0).sum(),
            '亏损周期': (returns_df['组合收益率'] < 0).sum(),
            '平均每周期收益': returns_df['组合收益率'].mean()
        }

        return metrics, returns_df, benchmark_returns

    def calculate_benchmark_returns(self, returns_df):
        """计算基准收益率"""
        benchmark_returns = []

        for _, row in returns_df.iterrows():
            start_date = row['开始日期']
            end_date = row['结束日期']

            # 获取基准价格数据
            benchmark_data = self.benchmark_data[
                (self.benchmark_data['日期'] >= start_date) &
                (self.benchmark_data['日期'] <= end_date)
            ].sort_values('日期')

            if len(benchmark_data) >= 2:
                start_price = benchmark_data.iloc[0]['收盘价']
                end_price = benchmark_data.iloc[-1]['收盘价']
                benchmark_return = end_price / start_price - 1
            else:
                benchmark_return = 0

            benchmark_returns.append(benchmark_return)

        return benchmark_returns

    def calculate_strategy_capacity(self, rebalance_records):
        """计算策略容量"""
        print("\n=== 计算策略容量 ===")

        daily_volumes = []

        for record in rebalance_records:
            rebalance_date = record['调仓日期']
            selected_stocks = record['选中股票']

            # 计算下一周的日均成交额
            next_week_start = rebalance_date + pd.Timedelta(days=1)
            next_week_end = rebalance_date + pd.Timedelta(days=7)

            total_volume = 0
            for stock in selected_stocks:
                code = stock['转债代码']

                volume_data = self.bond_data[
                    (self.bond_data['转债代码'] == code) &
                    (self.bond_data['交易日期'] >= next_week_start) &
                    (self.bond_data['交易日期'] <= next_week_end)
                ]

                if len(volume_data) > 0:
                    avg_volume = volume_data['日成交金额'].mean()
                    total_volume += avg_volume

            if total_volume > 0:
                daily_volumes.append(total_volume)

        # 计算V_market
        v_market = np.mean(daily_volumes) if daily_volumes else 0

        # 策略年化换手率
        annual_turnover = 2.0  # 每周换仓，年化换手率约为2

        # 三种情景的策略容量
        scenarios = {
            '悲观': 0.02,
            '中性': 0.10,
            '乐观': 0.20
        }

        capacity_results = {}
        for scenario, p in scenarios.items():
            capacity = v_market * p * 252 / annual_turnover
            capacity_results[scenario] = capacity

        return capacity_results, v_market, annual_turnover

    def save_results(self, metrics, returns_df, benchmark_returns, rebalance_records, capacity_results):
        """保存结果到Excel和图片"""
        print("\n=== 保存结果 ===")

        # 创建输出文件夹
        output_dir = "/Users/<USER>/Desktop/转债机器学习回测结果"
        os.makedirs(output_dir, exist_ok=True)

        # 保存Excel文件
        with pd.ExcelWriter(f"{output_dir}/回测结果.xlsx", engine='openpyxl') as writer:
            # Sheet1: 业绩指标
            metrics_df = pd.DataFrame([metrics]).T
            metrics_df.columns = ['数值']
            metrics_df.to_excel(writer, sheet_name='业绩指标')

            # Sheet2: 净值数据
            net_value_df = returns_df.copy()
            net_value_df['基准净值'] = (1 + pd.Series(benchmark_returns)).cumprod()
            net_value_df['策略净值'] = net_value_df['累计净值']
            net_value_df.to_excel(writer, sheet_name='净值数据', index=False)

            # Sheet3: 调仓记录
            rebalance_df = []
            for record in rebalance_records:
                for stock in record['选中股票']:
                    rebalance_df.append({
                        '调仓日期': record['调仓日期'],
                        '转债代码': stock['转债代码'],
                        '转债简称': stock['转债简称'],
                        '预测收益率': stock['预测收益率'],
                        '收盘价': stock['收盘价']
                    })

            pd.DataFrame(rebalance_df).to_excel(writer, sheet_name='调仓记录', index=False)

            # Sheet4: 策略容量
            capacity_df = pd.DataFrame([capacity_results]).T
            capacity_df.columns = ['策略容量(万元)']
            capacity_df.to_excel(writer, sheet_name='策略容量')

        # 生成图表
        self.generate_charts(returns_df, benchmark_returns, output_dir)

        print(f"结果已保存到：{output_dir}")

    def generate_charts(self, returns_df, benchmark_returns, output_dir):
        """生成图表"""

        # 设置图表样式
        plt.style.use('default')

        # 1. 净值走势图
        fig, ax = plt.subplots(figsize=(12, 8))

        dates = returns_df['开始日期']
        strategy_nav = returns_df['累计净值']
        benchmark_nav = (1 + pd.Series(benchmark_returns)).cumprod()

        ax.plot(dates, strategy_nav, label='Strategy', linewidth=2, color='red')
        ax.plot(dates, benchmark_nav, label='Benchmark', linewidth=2, color='blue')

        # 添加回撤阴影
        running_max = strategy_nav.expanding().max()
        ax.fill_between(dates, strategy_nav, running_max, alpha=0.3, color='gray')

        ax.set_title('Strategy vs Benchmark Performance', fontsize=16)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Cumulative Return', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/净值走势图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 年度收益率对比图
        fig, ax = plt.subplots(figsize=(10, 6))

        # 计算年度收益率
        returns_df['年份'] = returns_df['开始日期'].dt.year
        annual_returns = returns_df.groupby('年份')['组合收益率'].apply(lambda x: (1 + x).prod() - 1)

        # 基准年度收益率
        benchmark_df = pd.DataFrame({'基准收益率': benchmark_returns, '年份': returns_df['年份']})
        benchmark_annual = benchmark_df.groupby('年份')['基准收益率'].apply(lambda x: (1 + x).prod() - 1)

        x = np.arange(len(annual_returns))
        width = 0.35

        ax.bar(x - width/2, annual_returns.values, width, label='Strategy', color='red', alpha=0.7)
        ax.bar(x + width/2, benchmark_annual.values, width, label='Benchmark', color='blue', alpha=0.7)

        ax.set_title('Annual Returns Comparison', fontsize=16)
        ax.set_xlabel('Year', fontsize=12)
        ax.set_ylabel('Annual Return', fontsize=12)
        ax.set_xticks(x)
        ax.set_xticklabels(annual_returns.index)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/年度收益率对比.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("图表生成完成")

if __name__ == "__main__":
    # 创建策略实例
    strategy = ConvertibleBondStrategy()

    # 加载数据
    if strategy.load_data():
        # 数据探索
        strategy.data_exploration()

        # 数据预处理
        strategy.data_preprocessing()

        # 计算因子
        strategy.calculate_factors()

        # 运行回测
        portfolio_returns, rebalance_records = strategy.weekly_rebalance_backtest()

        if len(portfolio_returns) > 0:
            # 计算业绩指标
            metrics, returns_df, benchmark_returns = strategy.calculate_performance_metrics(portfolio_returns)

            # 计算策略容量
            capacity_results, v_market, annual_turnover = strategy.calculate_strategy_capacity(rebalance_records)

            # 保存结果
            strategy.save_results(metrics, returns_df, benchmark_returns, rebalance_records, capacity_results)

            # 打印关键指标
            print(f"\n=== 策略业绩总结 ===")
            print(f"年化收益率: {metrics['年化收益率']:.2%}")
            print(f"年化波动率: {metrics['年化波动']:.2%}")
            print(f"夏普比率: {metrics['夏普比率']:.2f}")
            print(f"最大回撤: {metrics['最大回撤']:.2%}")
            print(f"胜率: {metrics['胜率']:.2%}")
        else:
            print("回测失败，请检查数据")

        print("\n策略回测完成！")
    else:
        print("数据加载失败，请检查文件路径")
