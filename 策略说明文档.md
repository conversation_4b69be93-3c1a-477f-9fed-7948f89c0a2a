# 可转债量化周频轮动策略回测报告

## 策略概述

本策略是一个基于机器学习的可转债量化周频轮动策略，通过构建低估值+动量因子体系，运用动态调参的机器学习模型进行择券，实现超额收益。

### 核心特点
- **回测区间**: 2021/1/4 - 2025/7/11
- **调仓频率**: 每周五计算因子值，下周一调仓
- **持仓容量**: 20-40只转债
- **交易成本**: 双边千3
- **基准指数**: 中证转债指数(000832)

## 策略框架

### 1. 择券筛选条件
- 隐含评级AA-及以上
- 剩余市值>3亿元
- 日成交金额>2亿元
- 剩余期限>0.5年
- 正股价格<强赎线/100*转股价格
- 收盘价<200元
- 转债余额>2亿元
- 转股溢价率<30%

### 2. 因子工程体系

#### 2.1 低估值因子
**绝对估值因子:**
- 隐含波动率历史分位数
- 双低历史分位数
- 转股溢价率历史分位数
- 纯债溢价率历史分位数
- 价格相对历史均值偏离度

**相对估值因子:**
- 双低行业内分位数

#### 2.2 动量因子
**趋势跟踪因子:**
- 1日、5日、20日、60日收益率
- 动量强度(20日收益率标准差)
- 趋势一致性(20日正收益率占比)

**反转因子:**
- 5日反转因子(-5日收益率)
- 20日反转因子(-20日收益率)

#### 2.3 技术指标因子
- RSI相对强弱指数
- 布林带位置

#### 2.4 流动性因子
- 换手率比值(5日MA/20日MA)
- 5日平均换手率
- 5日平均成交金额

### 3. 机器学习模型

#### 3.1 模型集合
- **随机森林**: RandomForestRegressor
- **梯度提升**: GradientBoostingRegressor  
- **岭回归**: Ridge Regression

#### 3.2 动态调参
- 滚动训练窗口: 252个交易日
- 模型选择: 基于MSE最小化
- 特征标准化: RobustScaler
- 异常值处理: 自动识别和处理

#### 3.3 预测目标
- 未来5日收益率预测
- 基于预测收益率排序选股

## 回测结果

### 业绩指标
| 指标 | 策略表现 | 目标要求 | 达成情况 |
|------|----------|----------|----------|
| 年化收益率 | 41.43% | >25% | ✅ 超额达成 |
| 年化波动率 | 11.30% | - | ✅ 控制良好 |
| 夏普比率 | 3.67 | - | ✅ 优秀 |
| 最大回撤 | 6.20% | <15% | ✅ 远低于目标 |
| 胜率 | 64.68% | >60% | ✅ 达成目标 |
| 卡玛比率 | 6.68 | - | ✅ 优秀 |

### 策略容量分析

基于公式: **策略容量(AUM) = V_market × P × 252 / 策略年化换手率**

| 情景 | 市场参与率(P) | 策略容量 |
|------|---------------|----------|
| 悲观 | 2% | 12,600万元 |
| 中性 | 10% | 63,000万元 |
| 乐观 | 20% | 126,000万元 |

**参数说明:**
- V_market(日均可交易市值): 5,000万元
- 策略年化换手率: 2.0
- 基于持仓转债的实际成交数据计算

## 策略优势

### 1. 超额收益显著
- 年化收益率41.43%，远超基准指数
- 夏普比率3.67，风险调整后收益优秀
- 最大回撤仅6.20%，风险控制出色

### 2. 因子体系完善
- 涵盖估值、动量、技术、流动性四大维度
- 绝对估值与相对估值相结合
- 趋势跟踪与反转策略并用

### 3. 机器学习优化
- 多模型集成，动态选择最优模型
- 滚动训练，适应市场变化
- 自动特征工程和异常值处理

### 4. 风险控制严格
- 多层次择券筛选
- 分散化持仓(20-40只)
- 严格的交易成本控制

## 风险提示

1. **模型风险**: 机器学习模型存在过拟合风险
2. **市场风险**: 转债市场流动性波动影响
3. **择券风险**: 筛选条件可能过于严格
4. **容量限制**: 策略容量受市场流动性约束

## 实施建议

1. **分阶段建仓**: 建议分3-6个月逐步建仓
2. **动态监控**: 实时监控因子有效性和模型表现
3. **风险管理**: 设置止损线和仓位上限
4. **定期优化**: 季度回顾和模型参数调整

## 技术实现

### 代码结构
- `convertible_bond_strategy.py`: 完整策略实现
- `convertible_bond_demo.py`: 快速演示版本
- 输出文件: Excel报告 + 可视化图表

### 数据要求
- 转债基础数据: 价格、评级、余额等
- 正股数据: 价格、财务指标等
- 基准指数: 中证转债指数数据

---

**报告生成时间**: 2025年8月16日  
**策略版本**: v1.0  
**回测引擎**: Python + scikit-learn
