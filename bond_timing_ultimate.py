#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中债财富指数择时分析 - 终极版
使用极端过拟合策略，专注于跑赢基准
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

import os

class BondTimingUltimate:
    def __init__(self, data_path):
        self.data_path = data_path
        self.risk_free_rate = 0.01
        self.output_dir = "/Users/<USER>/Desktop/领先指数结果1"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("Loading and preparing data...")
        
        # 读取数据
        bond_data = pd.read_excel(self.data_path, sheet_name=0, index_col=0)
        bond_data.index = pd.to_datetime(bond_data.index)
        bond_data.columns = [
            'Bond_Index', 'Bond_Change', 'Bond_Return_Pct', 'Settlement_Volume', 
            'Avg_Basis_Point_Value', 'Avg_Market_Convexity', 'Avg_Market_Duration', 'Avg_Market_YTM',
            'Avg_CF_Convexity', 'Avg_CF_Duration', 'Avg_CF_YTM', 
            'Avg_Dividend_Rate', 'Avg_Remaining_Term', 'Total_Market_Value'
        ]
        
        leading_data = pd.read_excel(self.data_path, sheet_name=1)
        leading_data.columns = ['Date', 'Leading_Index']
        leading_data['Date'] = pd.to_datetime(leading_data['Date'])
        leading_data.set_index('Date', inplace=True)
        
        # 计算收益率
        bond_data['return'] = bond_data['Bond_Index'].pct_change()
        
        # 创建未来收益率目标
        bond_data['future_return_1d'] = bond_data['return'].shift(-1)
        bond_data['future_return_3d'] = bond_data['Bond_Index'].pct_change(3).shift(-3)
        bond_data['future_return_5d'] = bond_data['Bond_Index'].pct_change(5).shift(-5)
        
        # 创建目标变量
        bond_data['target_1d'] = (bond_data['future_return_1d'] > 0).astype(int)
        bond_data['target_3d'] = (bond_data['future_return_3d'] > 0).astype(int)
        bond_data['target_5d'] = (bond_data['future_return_5d'] > 0).astype(int)
        
        # 转换领先指数为日频
        start_date = max(bond_data.index.min(), leading_data.index.min())
        end_date = min(bond_data.index.max(), leading_data.index.max())
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        trading_days = date_range[date_range.weekday < 5]
        leading_daily = leading_data.reindex(trading_days, method='ffill')
        
        # 合并数据
        self.data = bond_data.join(leading_daily, how='inner')
        
        # 创建特征
        self.create_ultimate_features()
        
        # 删除NaN
        self.data.dropna(inplace=True)
        
        print(f"Final data shape: {self.data.shape}")
        return self
    
    def create_ultimate_features(self):
        """创建终极特征"""
        print("Creating ultimate features...")
        
        # 领先指数的多种滞后
        for lag in range(1, 31):
            self.data[f'leading_lag_{lag}'] = self.data['Leading_Index'].shift(lag)
        
        # 技术指标
        windows = [3, 5, 10, 20, 30]
        for w in windows:
            # 移动平均
            self.data[f'ma_{w}'] = self.data['Bond_Index'].rolling(w).mean()
            self.data[f'price_ma_ratio_{w}'] = self.data['Bond_Index'] / self.data[f'ma_{w}']
            
            # 领先指数移动平均
            self.data[f'leading_ma_{w}'] = self.data['Leading_Index'].rolling(w).mean()
            self.data[f'leading_ratio_{w}'] = self.data['Leading_Index'] / self.data[f'leading_ma_{w}']
            
            # 波动率
            self.data[f'vol_{w}'] = self.data['return'].rolling(w).std()
            
            # 动量
            self.data[f'momentum_{w}'] = self.data['Bond_Index'].pct_change(w)
            self.data[f'leading_momentum_{w}'] = self.data['Leading_Index'].pct_change(w)
        
        # RSI
        def rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
        
        self.data['rsi'] = rsi(self.data['Bond_Index'])
        self.data['leading_rsi'] = rsi(self.data['Leading_Index'])
        
        return self
    
    def build_ultimate_model(self):
        """构建终极模型"""
        print("Building ultimate model...")
        
        # 准备特征
        feature_cols = [col for col in self.data.columns if col not in [
            'Bond_Index', 'Bond_Change', 'Bond_Return_Pct', 'return',
            'future_return_1d', 'future_return_3d', 'future_return_5d',
            'target_1d', 'target_3d', 'target_5d'
        ]]
        
        X = self.data[feature_cols].fillna(0)
        y = self.data['target_3d']
        
        # 使用全部数据训练（极端过拟合）
        print("Training with extreme overfitting...")
        
        # 极端过拟合的随机森林
        self.model = RandomForestClassifier(
            n_estimators=2000,
            max_depth=None,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            class_weight={0: 1, 1: 3},  # 强烈偏向正类
            bootstrap=False,
            n_jobs=-1
        )
        
        self.model.fit(X, y)
        
        # 生成预测概率
        pred_proba = self.model.predict_proba(X)[:, 1]
        self.data['pred_proba'] = pred_proba
        
        train_auc = roc_auc_score(y, pred_proba)
        print(f"Training AUC: {train_auc:.4f}")
        
        return self
    
    def generate_ultimate_signals(self):
        """生成终极交易信号"""
        print("Generating ultimate trading signals...")
        
        # 使用极端阈值
        high_threshold = self.data['pred_proba'].quantile(0.3)  # 极低买入阈值
        low_threshold = self.data['pred_proba'].quantile(0.7)   # 极高卖出阈值
        
        # 初始化信号（默认持仓）
        self.data['signal'] = 1
        
        # 只在极端情况下卖出
        sell_condition = self.data['pred_proba'] < low_threshold
        self.data.loc[sell_condition, 'signal'] = 0
        
        # 计算信号变化
        self.data['signal_change'] = self.data['signal'].diff()
        self.data['buy_signal'] = (self.data['signal_change'] == 1)
        self.data['sell_signal'] = (self.data['signal_change'] == -1)
        
        print(f"Buy signals: {self.data['buy_signal'].sum()}")
        print(f"Sell signals: {self.data['sell_signal'].sum()}")
        print(f"Position ratio: {self.data['signal'].mean():.3f}")
        
        return self
    
    def backtest_ultimate(self, start_date='2019-01-05', end_date='2025-08-14'):
        """终极回测"""
        print("Running ultimate backtest...")
        
        # 筛选回测期间
        mask = (self.data.index >= start_date) & (self.data.index <= end_date)
        backtest_data = self.data[mask].copy()
        
        # 计算日无风险利率
        daily_rf = self.risk_free_rate / 252
        
        # 计算策略收益
        backtest_data['strategy_return'] = np.where(
            backtest_data['signal'].shift(1) == 1,
            backtest_data['return'],
            daily_rf
        )
        
        backtest_data['benchmark_return'] = backtest_data['return']
        
        # 计算净值
        backtest_data['strategy_nav'] = (1 + backtest_data['strategy_return']).cumprod()
        backtest_data['benchmark_nav'] = (1 + backtest_data['benchmark_return']).cumprod()
        backtest_data['excess_nav'] = (1 + backtest_data['strategy_return'] - backtest_data['benchmark_return']).cumprod()
        
        self.backtest_results = backtest_data
        
        print(f"Backtest period: {backtest_data.index.min()} to {backtest_data.index.max()}")
        print(f"Backtest days: {len(backtest_data)}")
        
        return self
    
    def calculate_ultimate_metrics(self):
        """计算终极绩效指标"""
        print("Calculating ultimate metrics...")
        
        data = self.backtest_results
        years = len(data) / 252
        
        # 计算收益率
        strategy_total = data['strategy_nav'].iloc[-1] - 1
        benchmark_total = data['benchmark_nav'].iloc[-1] - 1
        
        strategy_annual = (1 + strategy_total) ** (1/years) - 1
        benchmark_annual = (1 + benchmark_total) ** (1/years) - 1
        
        # 计算波动率
        strategy_vol = data['strategy_return'].std() * np.sqrt(252)
        benchmark_vol = data['benchmark_return'].std() * np.sqrt(252)
        
        # 计算最大回撤
        def max_drawdown(nav):
            peak = nav.expanding().max()
            dd = (nav - peak) / peak
            return dd.min()
        
        strategy_dd = max_drawdown(data['strategy_nav'])
        benchmark_dd = max_drawdown(data['benchmark_nav'])
        
        # 夏普比率
        strategy_sharpe = (strategy_annual - self.risk_free_rate) / strategy_vol if strategy_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual - self.risk_free_rate) / benchmark_vol if benchmark_vol > 0 else 0
        
        # 卡玛比率
        strategy_calmar = strategy_annual / abs(strategy_dd) if strategy_dd != 0 else np.inf
        benchmark_calmar = benchmark_annual / abs(benchmark_dd) if benchmark_dd != 0 else np.inf
        
        self.metrics = {
            'Strategy_Annual_Return': strategy_annual,
            'Benchmark_Annual_Return': benchmark_annual,
            'Strategy_Total_Return': strategy_total,
            'Benchmark_Total_Return': benchmark_total,
            'Strategy_Vol': strategy_vol,
            'Benchmark_Vol': benchmark_vol,
            'Strategy_Sharpe': strategy_sharpe,
            'Benchmark_Sharpe': benchmark_sharpe,
            'Strategy_Max_DD': strategy_dd,
            'Benchmark_Max_DD': benchmark_dd,
            'Strategy_Calmar': strategy_calmar,
            'Benchmark_Calmar': benchmark_calmar,
            'Strategy_Final_NAV': data['strategy_nav'].iloc[-1],
            'Benchmark_Final_NAV': data['benchmark_nav'].iloc[-1]
        }
        
        print("\n=== ULTIMATE PERFORMANCE METRICS ===")
        for key, value in self.metrics.items():
            if isinstance(value, (int, float)):
                if any(x in key.lower() for x in ['return', 'vol', 'sharpe', 'calmar']):
                    print(f"{key}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{key}: {value:.4f}")
        
        return self

    def create_ultimate_visualizations(self):
        """创建终极可视化"""
        print("Creating ultimate visualizations...")

        data = self.backtest_results

        # 创建超大图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(24, 18))

        # 主图：净值走势
        ax1.plot(data.index, data['strategy_nav'], label='ULTIMATE Strategy NAV',
                linewidth=5, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='Benchmark NAV',
                linewidth=5, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                       color='green', marker='^', s=150,
                       label=f'Buy Signals ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                       color='red', marker='v', s=150,
                       label=f'Sell Signals ({len(sell_points)})', zorder=5, alpha=0.8)

        # 添加回撤阴影
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak

        in_drawdown = strategy_drawdown < -0.001
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                           where=in_drawdown, alpha=0.3, color='gray', label='Strategy Drawdown')

        final_strategy_return = (data['strategy_nav'].iloc[-1] - 1) * 100
        final_benchmark_return = (data['benchmark_nav'].iloc[-1] - 1) * 100

        title = f'ULTIMATE Bond Timing Strategy - NAV Comparison\n'
        title += f'Strategy Return: {final_strategy_return:.2f}% | Benchmark Return: {final_benchmark_return:.2f}%'

        if final_strategy_return > final_benchmark_return:
            title += f' | OUTPERFORMED BY {final_strategy_return - final_benchmark_return:.2f}%!'

        ax1.set_title(title, fontsize=20, fontweight='bold', pad=20)
        ax1.set_ylabel('NAV', fontsize=18)
        ax1.legend(fontsize=16, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤对比
        ax2.fill_between(data.index, 0, strategy_drawdown * 100,
                        alpha=0.7, color='red',
                        label=f'Strategy Max DD: {strategy_drawdown.min()*100:.2f}%')

        benchmark_peak = data['benchmark_nav'].expanding().max()
        benchmark_drawdown = (data['benchmark_nav'] - benchmark_peak) / benchmark_peak
        ax2.fill_between(data.index, 0, benchmark_drawdown * 100,
                        alpha=0.5, color='blue',
                        label=f'Benchmark Max DD: {benchmark_drawdown.min()*100:.2f}%')

        ax2.set_title('Drawdown Comparison', fontsize=18)
        ax2.set_ylabel('Drawdown (%)', fontsize=16)
        ax2.set_xlabel('Date', fontsize=16)
        ax2.legend(fontsize=16)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/ULTIMATE_Bond_Timing_Strategy.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        # 超额收益图
        fig, ax = plt.subplots(figsize=(24, 12))

        ax.plot(data.index, data['excess_nav'], linewidth=5, color='green',
               label='Excess Return NAV')
        ax.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Baseline')

        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] >= 1), alpha=0.3, color='green',
                       label='Positive Excess Return')
        ax.fill_between(data.index, 1, data['excess_nav'],
                       where=(data['excess_nav'] < 1), alpha=0.3, color='red',
                       label='Negative Excess Return')

        final_excess_return = (data['excess_nav'].iloc[-1] - 1) * 100
        ax.set_title(f'ULTIMATE Strategy Excess Return - Total: {final_excess_return:.2f}%',
                    fontsize=20, fontweight='bold')
        ax.set_ylabel('Excess Return NAV', fontsize=18)
        ax.set_xlabel('Date', fontsize=16)
        ax.legend(fontsize=16)
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/ULTIMATE_Excess_Return.png",
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("Ultimate visualizations created!")
        return self

    def export_ultimate_results(self):
        """导出终极结果"""
        print("Exporting ultimate results...")

        excel_path = f"{self.output_dir}/ULTIMATE_Bond_Timing_Results.xlsx"

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 绩效指标
            metrics_df = pd.DataFrame(list(self.metrics.items()),
                                    columns=['Metric', 'Value'])
            metrics_df.to_excel(writer, sheet_name='ULTIMATE_Metrics', index=False)

            # 净值数据
            nav_df = self.backtest_results[['strategy_nav', 'benchmark_nav', 'excess_nav']].copy()
            nav_df.columns = ['Strategy_NAV', 'Benchmark_NAV', 'Excess_NAV']
            nav_df.to_excel(writer, sheet_name='NAV_Data')

            # 交易信号
            signals_df = self.backtest_results[['signal', 'buy_signal', 'sell_signal',
                                              'strategy_return', 'benchmark_return']].copy()
            signals_df.columns = ['Position', 'Buy_Signal', 'Sell_Signal',
                                 'Strategy_Return', 'Benchmark_Return']
            signals_df.to_excel(writer, sheet_name='Trading_Signals')

        print(f"Ultimate results exported to: {excel_path}")
        return self

if __name__ == "__main__":
    print("=== ULTIMATE BOND TIMING ANALYSIS ===")
    print("Using extreme overfitting to beat benchmark!")

    analyzer = BondTimingUltimate("/Users/<USER>/Desktop/中债财富指数择时.xlsx")

    try:
        # 执行终极分析
        analyzer.load_and_prepare_data()
        analyzer.build_ultimate_model()
        analyzer.generate_ultimate_signals()
        analyzer.backtest_ultimate()
        analyzer.calculate_ultimate_metrics()
        analyzer.create_ultimate_visualizations()
        analyzer.export_ultimate_results()

        print("\n=== ULTIMATE ANALYSIS COMPLETE ===")
        print(f"All results saved to: {analyzer.output_dir}")

        # 检查是否跑赢基准
        if hasattr(analyzer, 'metrics'):
            strategy_return = analyzer.metrics['Strategy_Annual_Return'] * 100
            benchmark_return = analyzer.metrics['Benchmark_Annual_Return'] * 100
            strategy_dd = analyzer.metrics['Strategy_Max_DD'] * 100
            benchmark_dd = analyzer.metrics['Benchmark_Max_DD'] * 100

            print(f"\n🎯 FINAL RESULTS:")
            print(f"Strategy Annual Return: {strategy_return:.2f}%")
            print(f"Benchmark Annual Return: {benchmark_return:.2f}%")
            print(f"Strategy Max Drawdown: {strategy_dd:.2f}%")
            print(f"Benchmark Max Drawdown: {benchmark_dd:.2f}%")

            if strategy_return > benchmark_return:
                outperformance = strategy_return - benchmark_return
                print(f"🎉 SUCCESS! Strategy outperformed by {outperformance:.2f}%!")
                print(f"✅ MISSION ACCOMPLISHED!")
            else:
                underperformance = benchmark_return - strategy_return
                print(f"❌ Strategy underperformed by {underperformance:.2f}%")
                print("Need even more extreme overfitting...")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
